<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 Bounce Sets Test with Detailed Logs</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #333;
            margin: 0;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
        }
        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        .canvas-section {
            text-align: center;
        }
        canvas {
            border: 3px solid #ddd;
            border-radius: 10px;
            background: #f9f9f9;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .controls-section {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .control-group {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #e9ecef;
        }
        .control-group h3 {
            margin: 0 0 15px 0;
            color: #495057;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .bounce-config {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        .bounce-input {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .bounce-input label {
            font-weight: bold;
            min-width: 60px;
            color: #495057;
        }
        .bounce-input input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 5px;
            font-size: 14px;
        }
        .buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        button {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            flex: 1;
            min-width: 120px;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .result-section {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        .result-section h3 {
            margin: 0 0 15px 0;
            color: #1976d2;
        }
        .result-display {
            font-size: 24px;
            font-weight: bold;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .result-heads {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            color: #8b4513;
        }
        .result-tails {
            background: linear-gradient(45deg, #c0c0c0, #e8e8e8);
            color: #2c2c2c;
        }
        .logs-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .logs-section h3 {
            margin: 0 0 15px 0;
            color: #495057;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .log-container {
            background: #212529;
            color: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }
        .log-bounce { color: #ffc107; }
        .log-selection { color: #17a2b8; }
        .log-audio { color: #6f42c1; }
        .log-config { color: #20c997; }
        .log-summary { color: #28a745; font-weight: bold; }
        .log-camera { color: #fd7e14; }
        .current-settings {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .current-settings h4 {
            margin: 0 0 10px 0;
            color: #155724;
        }
        .current-settings p {
            margin: 5px 0;
            color: #155724;
            font-family: monospace;
            font-size: 14px;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .stat-card h4 {
            margin: 0 0 10px 0;
            color: #495057;
            font-size: 14px;
        }
        .stat-card .value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            .buttons {
                flex-direction: column;
            }
            button {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Bounce Sets Test with Detailed Logs</h1>
            <p>ทดสอบการกำหนด set ของจำนวนการ bounce พร้อมแสดง logs รายละเอียด</p>
        </div>

        <div class="main-content">
            <div class="canvas-section">
                <canvas id="coinCanvas" width="350" height="350"></canvas>
            </div>

            <div class="controls-section">
                <div class="control-group">
                    <h3>⚙️ Bounce Sets Configuration</h3>
                    <div class="bounce-config">
                        <div class="bounce-input">
                            <label>หัว:</label>
                            <input type="text" id="headsInput" value="4,8" placeholder="เช่น 3,5,7">
                        </div>
                        <div class="bounce-input">
                            <label>ก้อย:</label>
                            <input type="text" id="tailsInput" value="6" placeholder="เช่น 4,6,8">
                        </div>
                        <button class="btn-success" onclick="updateSets()">🔄 Update Sets</button>
                    </div>
                </div>

                <div class="control-group">
                    <h3>🎮 Test Controls</h3>
                    <div class="buttons">
                        <button class="btn-primary" onclick="testFlip()">🎲 Random</button>
                        <button class="btn-info" onclick="testFlip('heads')">🪙 Force Heads</button>
                        <button class="btn-info" onclick="testFlip('tails')">🪙 Force Tails</button>
                        <button class="btn-warning" onclick="clearLogs()">🗑️ Clear Logs</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="current-settings">
            <h4>📊 Current Bounce Sets</h4>
            <p id="currentSettings">หัว: [4,8] | ก้อย: [6]</p>
        </div>

        <div class="result-section">
            <h3>🎯 Test Result</h3>
            <div id="resultDisplay" class="result-display" style="display: none;"></div>
            <div class="stats" id="statsContainer">
                <div class="stat-card">
                    <h4>Total Tests</h4>
                    <div class="value" id="totalTests">0</div>
                </div>
                <div class="stat-card">
                    <h4>Heads Count</h4>
                    <div class="value" id="headsCount">0</div>
                </div>
                <div class="stat-card">
                    <h4>Tails Count</h4>
                    <div class="value" id="tailsCount">0</div>
                </div>
                <div class="stat-card">
                    <h4>Last Bounce Count</h4>
                    <div class="value" id="lastBounceCount">-</div>
                </div>
            </div>
        </div>

        <div class="logs-section">
            <h3>
                📋 Detailed Test Logs
                <button class="btn-warning" onclick="clearLogs()" style="padding: 5px 10px; font-size: 12px;">Clear</button>
            </h3>
            <div class="log-container" id="logContainer">
                <div class="log-entry">🚀 Ready for testing...</div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="src/coin-flipper.js"></script>
    
    <script>
        let coinFlipper;
        let isFlipping = false;
        let testStats = {
            total: 0,
            heads: 0,
            tails: 0,
            lastBounceCount: 0
        };
        
        // Enhanced console logging
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            
            const logContainer = document.getElementById('logContainer');
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            
            // Color code different types of logs
            if (message.includes('🎯 Bounce Selection')) {
                logEntry.className += ' log-selection';
            } else if (message.includes('🏀 Bounce #')) {
                logEntry.className += ' log-bounce';
            } else if (message.includes('🎵 Audio') || message.includes('🎵 Sound')) {
                logEntry.className += ' log-audio';
            } else if (message.includes('🎯 Flip Test Configuration')) {
                logEntry.className += ' log-config';
            } else if (message.includes('✅ Bounce Test Summary')) {
                logEntry.className += ' log-summary';
            } else if (message.includes('📷 Camera')) {
                logEntry.className += ' log-camera';
            }
            
            logEntry.innerHTML = `<span style="color: #6c757d;">[${new Date().toLocaleTimeString()}]</span> ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        };
        
        async function init() {
            try {
                addLog('🚀 Initializing CoinFlipper with bounce sets...');
                
                coinFlipper = new CoinFlipper('coinCanvas', {
                    enableSound: true,
                    bounceSets: {
                        heads: [4, 8],
                        tails: [6]
                    }
                });
                
                await coinFlipper.ready();
                coinFlipper.startIdle();
                
                addLog('✅ CoinFlipper initialized successfully');
                updateCurrentSettings();
                updateStats();
                
            } catch (error) {
                addLog('❌ Initialization failed: ' + error.message);
            }
        }
        
        function addLog(message) {
            console.log(message);
        }
        
        function updateSets() {
            if (!coinFlipper) {
                addLog('❌ CoinFlipper not initialized');
                return;
            }
            
            const headsInput = document.getElementById('headsInput').value;
            const tailsInput = document.getElementById('tailsInput').value;
            
            try {
                const heads = headsInput.split(',').map(n => parseInt(n.trim())).filter(n => !isNaN(n) && n > 0);
                const tails = tailsInput.split(',').map(n => parseInt(n.trim())).filter(n => !isNaN(n) && n > 0);
                
                if (heads.length === 0 || tails.length === 0) {
                    addLog('❌ Invalid input: Please provide valid numbers');
                    return;
                }
                
                addLog('🔄 Updating bounce sets...');
                addLog('   New Heads Set: [' + heads.join(',') + ']');
                addLog('   New Tails Set: [' + tails.join(',') + ']');
                
                coinFlipper.setBounceSets({ heads, tails });
                updateCurrentSettings();
                
                addLog('✅ Bounce sets updated successfully');
                
            } catch (error) {
                addLog('❌ Error updating bounce sets: ' + error.message);
            }
        }
        
        function updateCurrentSettings() {
            if (!coinFlipper) return;
            
            const bounceSets = coinFlipper.getBounceSets();
            const settingsText = `หัว: [${bounceSets.heads.join(',')}] | ก้อย: [${bounceSets.tails.join(',')}]`;
            document.getElementById('currentSettings').textContent = settingsText;
        }
        
        function updateStats() {
            document.getElementById('totalTests').textContent = testStats.total;
            document.getElementById('headsCount').textContent = testStats.heads;
            document.getElementById('tailsCount').textContent = testStats.tails;
            document.getElementById('lastBounceCount').textContent = testStats.lastBounceCount || '-';
        }
        
        async function testFlip(result = null) {
            if (isFlipping || !coinFlipper) {
                addLog('⏳ Already flipping or not initialized');
                return;
            }
            
            isFlipping = true;
            const resultDisplay = document.getElementById('resultDisplay');
            resultDisplay.style.display = 'none';
            
            try {
                addLog('');
                addLog('🎬 ========== STARTING NEW TEST ==========');
                addLog('🪙 Test Parameters: ' + (result ? `Force ${result}` : 'Random flip'));
                
                const finalResult = await coinFlipper.toss(result);
                
                // Update stats
                testStats.total++;
                if (finalResult === 'heads') {
                    testStats.heads++;
                } else {
                    testStats.tails++;
                }
                
                // Extract bounce count from logs (this is a simple way, in real implementation you might want to return this from the flip method)
                // For now, we'll use the current bounce sets to show expected range
                const bounceSets = coinFlipper.getBounceSets();
                const expectedSet = bounceSets[finalResult] || [];
                
                addLog('🎬 ========== TEST COMPLETED ==========');
                addLog(`🏆 Final Result: ${finalResult.toUpperCase()}`);
                addLog(`📊 Expected Bounce Range: [${expectedSet.join(',')}]`);
                addLog('');
                
                // Display result
                resultDisplay.textContent = `${finalResult === 'heads' ? '🪙 หัว (Heads)' : '🪙 ก้อย (Tails)'}`;
                resultDisplay.className = `result-display ${finalResult === 'heads' ? 'result-heads' : 'result-tails'}`;
                resultDisplay.style.display = 'block';
                
                updateStats();
                
            } catch (error) {
                addLog('❌ Flip error: ' + error.message);
                resultDisplay.textContent = '❌ Error occurred';
                resultDisplay.className = 'result-display';
                resultDisplay.style.display = 'block';
            } finally {
                isFlipping = false;
            }
        }
        
        function clearLogs() {
            const logContainer = document.getElementById('logContainer');
            logContainer.innerHTML = '<div class="log-entry">🗑️ Logs cleared - Ready for new tests...</div>';
        }
        
        // Initialize when page loads
        window.addEventListener('load', init);
    </script>
</body>
</html>
